import { useState } from 'react';
import ex1 from '../../../assets/images/reminder.png';
import { ArrowCircleRight2, CloseCircle } from 'iconsax-react';
import { events, GuestData } from '../../../lib/services/events';

interface Template {
  id?: string;
  name: string;
  preview_url?: string;
  image?: string;
}

interface Reminder {
  id: number;
  text: string;
  checked: boolean;
}

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface PreviewProps {
  onSuccess: () => void;
  selectedTemplate: Template | null;
  guests: Guest[];
  eventId?: string;
}

export const Preview = ({
  onSuccess,
  selectedTemplate,
  guests,
  eventId,
}: PreviewProps) => {
  console.log(selectedTemplate, 'selected template');

  const [reminders, setReminders] = useState([
    { id: 1, text: 'A week before the event day', checked: true },
    { id: 2, text: '72 Hours before event day', checked: true },
    { id: 3, text: '24 Hours before event day', checked: true },
    { id: 4, text: 'On the event day', checked: false },
  ]);

  const templateImageUrl =
    selectedTemplate?.preview_url || selectedTemplate?.image || ex1;

  const toggleCheck = (id: number): void => {
    setReminders(
      reminders.map((reminder: Reminder) =>
        reminder.id === id
          ? { ...reminder, checked: !reminder.checked }
          : reminder
      )
    );
  };

  const handleCreateGuestList = async () => {
    try {
      if (eventId && guests.length > 0) {
        const guestData: GuestData[] = guests.map((guest) => ({
          email: guest.email,
          first_name: guest.firstName,
          last_name: guest.lastName,
          phone_number: guest.phone,
        }));

        await events.createGuestForAuthUsers({
          eventId,
          guests: guestData,
        });
      }

      // onSuccess();
    } catch (error) {
      console.error('Failed to create guest list:', error);
    }
  };
  return (
    <div className="max-w-[645px] mx-auto pt-5 md:pt-12 px-4 md:px-0 font-rethink  pb-52">
      <div className="md:ml-9 w-full  ">
        <div className="flex mb-6 justify-between items-end">
          <h2 className="md:text-[40px] text-base font-medium leading-[114.99999999999999%]">
            Preview your guest list.
            <br />
            You are good to go!
          </h2>
          <button
            type="button"
            onClick={handleCreateGuestList}
            className="bg-primary-650 cursor-pointer text-white flex items-center gap-2 px-3 py-2 rounded-full text-xs md:text-base font-semibold">
            <span>Create GuestList</span>
            <ArrowCircleRight2 color="#FFFFFF" size="24" variant="Bulk" />
          </button>
        </div>
        <div className="bg-white rounded-[20px] md:flex justify-between">
          <div className="md:max-w-[265px] w-full border-r border-grey-850">
            <img
              src={templateImageUrl}
              alt={selectedTemplate?.name || 'Invitation Template'}
              className="w-full h-auto object-contain rounded-t-2xl"
              style={{
                aspectRatio: '265/300',
                maxHeight: '300px',
              }}
            />
            <div className="px-5 mt-4">
              <h1 className="text-sm mb-4 font-bold text-grey-700 tracking-[0.16em]">
                SEND REMINDERS
              </h1>

              <div className="space-y-8">
                {reminders.map((reminder) => (
                  <div key={reminder.id} className="flex items-center">
                    <div
                      className={`w-5.5 h-5.5 rounded-full flex items-center justify-center mr-2 cursor-pointer
                  ${
                    reminder.checked
                      ? 'bg-primary-650'
                      : 'bg-white border border-primary-650'
                  }`}
                      onClick={() => toggleCheck(reminder.id)}>
                      {reminder.checked && (
                        <svg
                          width={24}
                          height={24}
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M9 12L11 14L15 10"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      )}
                    </div>
                    <span
                      className={`text-sm  font-semibold ${
                        reminder.checked
                          ? 'text-dark-blue-400 italic'
                          : 'text-grey-250'
                      }`}>
                      {reminder.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="flex-1 mt-10 md:mt-0">
            <h2 className="py-[19px] pl-4 text-lg font-medium border-b border-grey-150 shadow-[0px_12px_120px_0px_#5F5F5F0F]">
              Guests
            </h2>
            <div className="px-6 pt-5">
              {guests.map((guest, index) => (
                <div
                  key={index}
                  className="w-full rounded-2xl mb-3 bg-white border border-grey-150 py-4  pl-4 pr-2.5 flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center ">
                      <div className="w-10 h-10 bg-gradient-to-br from-[#FEF7F4] from-26.3% to-[#F5F6FE] to-75.01% rounded-full flex items-center justify-center text-dark-blue-200 font-semibold text-base">
                        {(guest?.firstName?.charAt(0)?.toUpperCase() || '') +
                          (guest?.lastName?.charAt(0)?.toUpperCase() || '')}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-dark-blue-400 font-semibold text-sm">
                        {guest.firstName + ' ' + guest.lastName}
                      </h3>
                      <p className="text-grey-650 text-xs">
                        {guest.email} • {guest.phone}
                      </p>
                    </div>
                  </div>
                  <button className="cursor-pointer">
                    <CloseCircle size="20" color="#000059" variant="Bulk" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
