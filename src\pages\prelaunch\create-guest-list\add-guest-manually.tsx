import { useState, useEffect } from 'react';
import { ArrowDown2 } from 'iconsax-react';
import { FormInput } from '../../../components/inputs/form-input/form-input';
import { GuestPreview } from './guest-preview';

const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);

    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
};

interface AddGuestManuallyProps {
  onNextStep?: () => void;
  onFormActiveChange?: (isActive: boolean) => void;
}

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface AvatarProps {
  initials: string;
  color?: string;
  offset?: boolean;
}


export const AddGuestManually = ({
  onNextStep,
  onFormActiveChange,
}: AddGuestManuallyProps) => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [guests, setGuests] = useState<Guest[]>([]);
  const [allGuests, setAllGuests] = useState<Guest[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const isMobile = useMediaQuery('(max-width: 767px)');


  useEffect(() => {
    const isFormActive =
      firstName !== '' ||
      lastName !== '' ||
      email !== '' ||
      mobileNumber !== '';
    onFormActiveChange?.(isFormActive);
  }, [firstName, lastName, email, mobileNumber, onFormActiveChange]);

  const handleAddToQueue = () => {
    if (firstName && lastName && email && mobileNumber) {
      const newGuest: Guest = {
        id: Date.now(),
        firstName,
        lastName,
        email,
        phone: mobileNumber,
      };

      setGuests([...guests, newGuest]);
      setAllGuests([...allGuests, newGuest]);
      setFirstName('');
      setLastName('');
      setEmail('');
      setMobileNumber('');
    }
  };

  const handleAddGuest = () => {
    if (guests.length === 0 && firstName && lastName && email && mobileNumber) {
      const newGuest: Guest = {
        id: Date.now(),
        firstName,
        lastName,
        email,
        phone: mobileNumber,
      };

      setGuests([...guests, newGuest]);
      setAllGuests([...allGuests, newGuest]);
      if (onNextStep) {
        onNextStep();
      }
    }

    // if (
    //   allGuests.length > 0 ||
    //   (firstName && lastName && email && mobileNumber)
    // ) {
    //   setShowPreview(true);
    // }
  };

  const handleRemoveGuest = (id: number) => {
    setGuests(guests.filter((guest) => guest.id !== id));
    setAllGuests(allGuests.filter((guest) => guest.id !== id));
  };

  const handleAddMoreGuests = () => {
    setShowPreview(false);
  };

  if (showPreview) {
    return (
      <GuestPreview
        guests={allGuests}
        onRemoveGuest={handleRemoveGuest}
        onAddMoreGuests={handleAddMoreGuests}
      />
    );
  }

  const guestList = allGuests.map((guest) => ({
    id: guest.id,
    initials: `${guest.firstName.charAt(0)}${guest.lastName.charAt(0)}`,
  }));

  // Calculate how many avatars to show based on screen size
  const visibleAvatarCount = isMobile ? 3 : 7;
  const additionalGuests =
    guestList.length > visibleAvatarCount
      ? guestList.length - visibleAvatarCount
      : 0;

  const Avatar = ({
    initials,
    color = 'bg-light-blue',
    offset = false,
  }: AvatarProps) => {
    return (
      <div
        className={`${color} text-dark-blue-400 font-inter text-base w-10 h-10 rounded-full flex items-center justify-center font-medium border-2 border-white ${
          offset ? '-ml-2' : ''
        }`}>
        {initials}
      </div>
    );
  };

  return (
    <div className="flex-1 pt-8 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium">
        Add guests manually
      </h3>
      <p className="md:text-base text-sm text-grey-250 mb-5">
        add a guest to your event manually
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormInput
          label="Guest First name"
          placeholder="Enter first name"
          value={firstName}
          onChange={(e) => setFirstName(e.target.value)}
        />

        <FormInput
          label="Guest Last name"
          placeholder="Enter last name"
          value={lastName}
          onChange={(e) => setLastName(e.target.value)}
        />
      </div>

      <FormInput
        label="Email"
        placeholder="Enter your guest's email"
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />

      <FormInput
        label="Mobile Number"
        placeholder="Enter mobile number"
        type="tel"
        value={mobileNumber}
        onChange={(e) => setMobileNumber(e.target.value)}
        leftAddon={
          <div className="flex items-center">
            <span className="text-grey-500 font-semibold mr-1 italic text-base">
              +234
            </span>
            <ArrowDown2 size={16} color="#717680" />
          </div>
        }
      />

      <button
        onClick={handleAddToQueue}
        className="bg-primary-250 hover:bg-primary-250/50 text-primary  text-sm cursor-pointer font-medium py-1 px-3 rounded-full flex items-center mb-2.5">
        <span className="bg-primary text-white text-xs  rounded-full h-3 w-3 flex items-center justify-center mr-2">
          +
        </span>
        Add to Queue
      </button>

      <div className="flex items-center py-2">
        {guestList.slice(0, visibleAvatarCount).map((guest, index) => (
          <Avatar key={guest.id} initials={guest.initials} offset={index > 0} />
        ))}

        {additionalGuests > 0 && (
          <div className="bg-light-blue text-grey-700 w-10 h-10 rounded-full flex items-center justify-center font-medium -ml-2 border-2 border-white">
            +{additionalGuests}
          </div>
        )}
      </div>
      <div className="mt-2.5 py-3.5 border-t border-grey-850 flex justify-end">
        <button
          onClick={handleAddGuest}
          className="bg-primary cursor-pointer text-base font-semibold mr-5 text-white h-12 max-w-[135px] w-full rounded-full hover:bg-primary/80 transition-colors">
          Add Guest
        </button>
      </div>
    </div>
  );
};
